/**
 * Web Worker for Heavy Data Generation
 * Moves expensive data generation off the main thread
 */

// Data generation functions moved to worker
function generateTodayVsPreviousYearsDataAsync(config) {
  const data = [];
  const { startYear, endYear, marketplaceCodes, pacificMonthDay } = config;
  
  // Use requestIdleCallback pattern for chunked processing
  return new Promise((resolve) => {
    let currentYear = startYear;
    
    function processChunk() {
      const chunkSize = 5; // Process 5 years at a time
      const endChunk = Math.min(currentYear + chunkSize, endYear + 1);
      
      for (let year = currentYear; year < endChunk; year++) {
        // Generate data for this year (same logic as original)
        const yearData = generateYearData(year, marketplaceCodes, pacificMonthDay);
        data.push(yearData);
      }
      
      currentYear = endChunk;
      
      if (currentYear <= endYear) {
        // Schedule next chunk
        setTimeout(processChunk, 0);
      } else {
        resolve(data);
      }
    }
    
    processChunk();
  });
}

function generateMonthlySalesDataAsync(year, monthsToShow) {
  return new Promise((resolve) => {
    const monthlyData = [];
    let currentMonth = 0;
    
    function processMonthChunk() {
      const chunkSize = 3; // Process 3 months at a time
      const endChunk = Math.min(currentMonth + chunkSize, monthsToShow);
      
      for (let monthIndex = currentMonth; monthIndex < endChunk; monthIndex++) {
        // Generate month data (same logic as original)
        const monthData = generateMonthData(monthIndex);
        monthlyData.push(monthData);
      }
      
      currentMonth = endChunk;
      
      if (currentMonth < monthsToShow) {
        setTimeout(processMonthChunk, 0);
      } else {
        resolve(monthlyData);
      }
    }
    
    processMonthChunk();
  });
}

// Helper functions
function generateYearData(year, marketplaceCodes, pacificMonthDay) {
  // Original year data generation logic here
  // (moved from generateTodayVsPreviousYearsData)
}

function generateMonthData(monthIndex) {
  // Original month data generation logic here
  // (moved from generateMonthlySalesDataForYear)
}

// Worker message handler
self.onmessage = async function(e) {
  const { type, config } = e.data;
  
  try {
    let result;
    
    switch (type) {
      case 'generateTodayVsPreviousYears':
        result = await generateTodayVsPreviousYearsDataAsync(config);
        break;
      case 'generateMonthlySales':
        result = await generateMonthlySalesDataAsync(config.year, config.monthsToShow);
        break;
      default:
        throw new Error(`Unknown task type: ${type}`);
    }
    
    self.postMessage({ success: true, data: result });
  } catch (error) {
    self.postMessage({ success: false, error: error.message });
  }
};
