<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Real-Time Percentage Calculations Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid;
        }
        .test-pass {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .test-fail {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .controls {
            margin: 20px 0;
        }
        .controls button {
            margin: 5px;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background-color: #007bff;
            color: white;
        }
        .controls button:hover {
            background-color: #0056b3;
        }
        .status {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .percentage-display {
            font-size: 18px;
            font-weight: bold;
            margin: 10px 0;
        }
        .positive { color: #28a745; }
        .negative { color: #dc3545; }
    </style>
</head>
<body>
    <h1>Real-Time Percentage Calculations Test</h1>
    
    <div class="test-container">
        <h2>Test Controls</h2>
        <div class="controls">
            <button onclick="testPercentageCalculations()">Test Percentage Calculations</button>
            <button onclick="startRealTimeUpdates()">Start Real-Time Updates</button>
            <button onclick="stopRealTimeUpdates()">Stop Real-Time Updates</button>
            <button onclick="manualRefresh()">Manual Refresh</button>
            <button onclick="getStatus()">Get Status</button>
        </div>
    </div>

    <div class="test-container">
        <h2>Expected vs Actual Percentages</h2>
        <div id="percentage-results"></div>
    </div>

    <div class="test-container">
        <h2>Real-Time Manager Status</h2>
        <div id="status-display" class="status">
            Status will appear here...
        </div>
    </div>

    <div class="test-container">
        <h2>Test Results</h2>
        <div id="test-results"></div>
    </div>

    <script>
        // Mock the dashboard functions for testing
        window.MARKETPLACE_DISTRIBUTION = {
            us: 0.45,
            uk: 0.20,
            de: 0.15,
            fr: 0.08,
            it: 0.06,
            es: 0.04,
            jp: 0.02
        };

        // Expected percentage calculations based on the fixed data
        const expectedPercentages = {
            currentMonth: ((2847 - 4523) / 4523 * 100).toFixed(1), // -37.1%
            lastMonth: ((4523 - 3987) / 3987 * 100).toFixed(1),    // +13.4%
            currentYear: ((28947 - 45234) / 45234 * 100).toFixed(1), // -36.0%
            lastYear: ((45234 - 38765) / 38765 * 100).toFixed(1)     // +16.7%
        };

        function addTestResult(message, passed) {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${passed ? 'test-pass' : 'test-fail'}`;
            resultDiv.innerHTML = `
                <strong>${passed ? '✅ PASS' : '❌ FAIL'}</strong>: ${message}
                <br><small>Time: ${new Date().toLocaleTimeString()}</small>
            `;
            resultsDiv.appendChild(resultDiv);
        }

        function updatePercentageDisplay(period, expected, actual, isValid) {
            const resultsDiv = document.getElementById('percentage-results');
            const periodDiv = document.getElementById(`period-${period}`) || document.createElement('div');
            periodDiv.id = `period-${period}`;
            
            const actualValue = actual !== null ? actual.toFixed(1) : 'N/A';
            const isPositive = actual >= 0;
            const matches = Math.abs(parseFloat(expected) - actual) < 0.1;
            
            periodDiv.innerHTML = `
                <div style="border: 1px solid #ddd; padding: 10px; margin: 5px 0; border-radius: 4px;">
                    <strong>${period.toUpperCase()}</strong><br>
                    Expected: <span class="${parseFloat(expected) >= 0 ? 'positive' : 'negative'}">${expected}%</span><br>
                    Actual: <span class="${isPositive ? 'positive' : 'negative'}">${actualValue}%</span><br>
                    Valid: ${isValid ? '✅' : '❌'}<br>
                    Match: ${matches ? '✅' : '❌'}
                </div>
            `;
            
            if (!document.getElementById(`period-${period}`)) {
                resultsDiv.appendChild(periodDiv);
            }
        }

        function testPercentageCalculations() {
            console.log('🧮 Testing percentage calculations...');
            
            if (typeof window.generateFourSalesCardsMockData !== 'function') {
                addTestResult('generateFourSalesCardsMockData function not available', false);
                return;
            }

            if (typeof window.calculateComparisonPercentage !== 'function') {
                addTestResult('calculateComparisonPercentage function not available', false);
                return;
            }

            try {
                const mockData = window.generateFourSalesCardsMockData(true);
                const cardPeriods = ['currentMonth', 'lastMonth', 'currentYear', 'lastYear'];
                let allTestsPassed = true;

                cardPeriods.forEach(period => {
                    const periodData = mockData[period];
                    if (periodData) {
                        const comparisonData = window.calculateComparisonPercentage(
                            periodData.totalSales, 
                            periodData.previousSales
                        );
                        
                        const expected = parseFloat(expectedPercentages[period]);
                        const actual = comparisonData.percentage;
                        const matches = Math.abs(expected - actual) < 0.1;
                        
                        updatePercentageDisplay(period, expectedPercentages[period], actual, comparisonData.isValid);
                        
                        if (!matches || !comparisonData.isValid) {
                            allTestsPassed = false;
                            addTestResult(
                                `${period}: Expected ${expectedPercentages[period]}%, got ${actual}% (valid: ${comparisonData.isValid})`,
                                false
                            );
                        } else {
                            addTestResult(
                                `${period}: Correct percentage ${actual}%`,
                                true
                            );
                        }
                    }
                });

                if (allTestsPassed) {
                    addTestResult('All percentage calculations are correct!', true);
                }

            } catch (error) {
                addTestResult(`Error during testing: ${error.message}`, false);
                console.error('Test error:', error);
            }
        }

        function startRealTimeUpdates() {
            if (window.fourSalesCardsControls) {
                window.fourSalesCardsControls.startRealTimeUpdates(10000); // 10 seconds for testing
                addTestResult('Real-time updates started (10 second intervals)', true);
                updateStatus();
            } else {
                addTestResult('fourSalesCardsControls not available', false);
            }
        }

        function stopRealTimeUpdates() {
            if (window.fourSalesCardsControls) {
                window.fourSalesCardsControls.stopRealTimeUpdates();
                addTestResult('Real-time updates stopped', true);
                updateStatus();
            } else {
                addTestResult('fourSalesCardsControls not available', false);
            }
        }

        function manualRefresh() {
            if (window.fourSalesCardsControls) {
                window.fourSalesCardsControls.refreshNow().then(() => {
                    addTestResult('Manual refresh completed', true);
                    testPercentageCalculations(); // Re-test after refresh
                }).catch(error => {
                    addTestResult(`Manual refresh failed: ${error.message}`, false);
                });
            } else {
                addTestResult('fourSalesCardsControls not available', false);
            }
        }

        function getStatus() {
            updateStatus();
        }

        function updateStatus() {
            const statusDiv = document.getElementById('status-display');
            
            if (window.fourSalesCardsControls) {
                const status = window.fourSalesCardsControls.getStatus();
                if (status) {
                    statusDiv.innerHTML = `
                        <strong>Real-Time Manager Status:</strong><br>
                        Active: ${status.isActive ? '✅ Yes' : '❌ No'}<br>
                        Refresh Frequency: ${status.refreshFrequency}ms<br>
                        Last Refresh: ${status.lastRefreshTime ? new Date(status.lastRefreshTime).toLocaleTimeString() : 'Never'}<br>
                        Error Count: ${status.errorCount}<br>
                        Time Since Last Refresh: ${status.timeSinceLastRefresh ? Math.round(status.timeSinceLastRefresh / 1000) + 's' : 'N/A'}
                    `;
                } else {
                    statusDiv.innerHTML = 'Status not available';
                }
            } else {
                statusDiv.innerHTML = 'fourSalesCardsControls not available';
            }
        }

        // Listen for refresh events
        document.addEventListener('fourSalesCardsRefreshed', (event) => {
            addTestResult(`Auto-refresh completed in ${event.detail.refreshTime.toFixed(2)}ms`, true);
            testPercentageCalculations(); // Re-test after auto-refresh
        });

        // Initial status update
        setTimeout(updateStatus, 1000);
    </script>
</body>
</html>
