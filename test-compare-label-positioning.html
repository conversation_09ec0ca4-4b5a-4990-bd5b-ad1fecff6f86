<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Compare Mode Label Positioning Test</title>
    <link rel="stylesheet" href="components/charts/snap-charts.css">
    <style>
        body {
            font-family: 'Amazon Ember', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .test-description {
            font-size: 14px;
            color: #666;
            margin-bottom: 20px;
        }
        .chart-container {
            width: 100%;
            height: 300px;
        }
        .toggle-container {
            margin: 20px 0;
            text-align: center;
        }
        .toggle-button {
            background: #6033FF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 0 10px;
        }
        .toggle-button:hover {
            background: #4f2acc;
        }
        .toggle-button.active {
            background: #26D1A5;
        }
        .status {
            text-align: center;
            font-weight: bold;
            margin: 10px 0;
        }
        .status.compare-on {
            color: #26D1A5;
        }
        .status.compare-off {
            color: #666;
        }
    </style>
</head>
<body>
    <h1>Compare Mode Label Positioning Test</h1>
    <p>This test demonstrates the fix for X-axis label positioning when compare mode is enabled. 
       Labels should be centered under the main columns (darker blue), not under the comparison columns (lighter blue).</p>

    <div class="toggle-container">
        <button id="toggleCompare" class="toggle-button">Toggle Compare Mode</button>
        <div id="status" class="status compare-off">Compare Mode: OFF</div>
    </div>

    <div class="test-container">
        <div class="test-title">Stacked Column Chart - Compare Mode Test</div>
        <div class="test-description">
            When compare mode is ON, the month/year labels (JAN '25, FEB '25, etc.) should be centered under the main columns (darker blue), 
            not under the comparison columns (lighter blue) or between both columns.
        </div>
        <div id="chart1" class="chart-container"></div>
    </div>

    <script src="components/charts/snap-charts.js"></script>
    <script>
        // Sample data for testing
        const testData = [
            { month: 'JAN', day: '15', year: '25', sales: 150, royalties: 45, values: [50, 30, 40, 30], labels: ['US', 'UK', 'DE', 'FR'] },
            { month: 'FEB', day: '15', year: '25', sales: 200, royalties: 60, values: [80, 40, 50, 30], labels: ['US', 'UK', 'DE', 'FR'] },
            { month: 'MAR', day: '15', year: '25', sales: 180, royalties: 54, values: [70, 35, 45, 30], labels: ['US', 'UK', 'DE', 'FR'] },
            { month: 'APR', day: '15', year: '25', sales: 220, royalties: 66, values: [90, 45, 55, 30], labels: ['US', 'UK', 'DE', 'FR'] },
            { month: 'MAY', day: '15', year: '25', sales: 190, royalties: 57, values: [75, 40, 45, 30], labels: ['US', 'UK', 'DE', 'FR'] }
        ];

        const compareData = [
            { month: 'JAN', day: '15', year: '24', sales: 120, royalties: 36, values: [40, 25, 35, 20], labels: ['US', 'UK', 'DE', 'FR'] },
            { month: 'FEB', day: '15', year: '24', sales: 160, royalties: 48, values: [60, 30, 40, 30], labels: ['US', 'UK', 'DE', 'FR'] },
            { month: 'MAR', day: '15', year: '24', sales: 140, royalties: 42, values: [55, 25, 35, 25], labels: ['US', 'UK', 'DE', 'FR'] },
            { month: 'APR', day: '15', year: '24', sales: 170, royalties: 51, values: [65, 35, 40, 30], labels: ['US', 'UK', 'DE', 'FR'] },
            { month: 'MAY', day: '15', year: '24', sales: 150, royalties: 45, values: [60, 30, 35, 25], labels: ['US', 'UK', 'DE', 'FR'] }
        ];

        let chart1;
        let compareMode = false;

        function initializeChart() {
            chart1 = new SnapChart({
                container: '#chart1',
                type: 'stacked-column',
                data: testData,
                options: {
                    title: 'Monthly Sales Comparison',
                    subtitle: 'Stacked column chart with compare mode toggle',
                    compareMode: compareMode,
                    compareData: compareMode ? compareData : null,
                    animate: true,
                    responsive: true
                },
                demoOptions: {
                    showContainer: true,
                    showTitle: false,
                    showDataEditor: false,
                    showControls: false,
                    showInsights: false
                }
            });
        }

        function toggleCompareMode() {
            compareMode = !compareMode;
            
            // Update status
            const status = document.getElementById('status');
            const button = document.getElementById('toggleCompare');
            
            if (compareMode) {
                status.textContent = 'Compare Mode: ON';
                status.className = 'status compare-on';
                button.classList.add('active');
            } else {
                status.textContent = 'Compare Mode: OFF';
                status.className = 'status compare-off';
                button.classList.remove('active');
            }

            // Recreate chart with new compare mode
            if (chart1) {
                chart1.containerElement.innerHTML = '';
            }
            
            chart1 = new SnapChart({
                container: '#chart1',
                type: 'stacked-column',
                data: testData,
                options: {
                    title: 'Monthly Sales Comparison',
                    subtitle: compareMode ? 'Compare mode ON - Labels should be under main columns (darker)' : 'Compare mode OFF - Normal positioning',
                    compareMode: compareMode,
                    compareData: compareMode ? compareData : null,
                    animate: true,
                    responsive: true
                },
                demoOptions: {
                    showContainer: true,
                    showTitle: false,
                    showDataEditor: false,
                    showControls: false,
                    showInsights: false
                }
            });
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            initializeChart();
            
            // Add event listener for toggle button
            document.getElementById('toggleCompare').addEventListener('click', toggleCompareMode);
        });
    </script>
</body>
</html>
