<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Header Responsive Test</title>
    <link rel="stylesheet" href="snapapp.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Amazon Ember', Arial, sans-serif;
            background: var(--bg-primary, #f8f9fa);
        }
        
        /* Test container to simulate different widths */
        .test-container {
            border: 2px solid #007bff;
            margin: 20px;
            resize: horizontal;
            overflow: auto;
            min-width: 800px;
            max-width: 100%;
            width: 1600px;
        }
        
        .width-indicator {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #007bff;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
            z-index: 10000;
        }
        
        /* Override fixed positioning for test */
        .dashboard-header {
            position: relative !important;
            left: 0 !important;
            right: auto !important;
            top: 0 !important;
        }
    </style>
</head>
<body>
    <div class="width-indicator" id="widthIndicator">Width: <span id="widthValue">0px</span></div>
    
    <h2 style="margin: 20px;">Header Responsive Test - Resize the blue container to test responsiveness</h2>
    
    <div class="test-container" id="testContainer">
        <div class="dashboard-header">
            <div class="dashboard-header-content">
                <h1>Dashboard</h1>
                <div class="database-container">
                    <div class="database-row">
                        <div class="database-left">
                            <!-- Time Tracker Components -->
                            <div class="time-tracker-container">
                                <!-- Local Time Block -->
                                <div class="time-tracker-block">
                                    <span class="time-tracker-label">Local Time</span>
                                    <div class="time-tracker-content">
                                        <img src="./assets/timezone-ic.svg" alt="Timezone Icon" class="time-tracker-icon" width="12" height="12" />
                                        <div class="time-tracker-time">11:31:25 AM</div>
                                    </div>
                                </div>

                                <!-- Divider between time trackers -->
                                <div class="time-tracker-divider"></div>

                                <!-- Pacific Time Block -->
                                <div class="time-tracker-block">
                                    <span class="time-tracker-label">Pacific Time</span>
                                    <div class="time-tracker-content">
                                        <img src="./assets/timezone-ic.svg" alt="Timezone Icon" class="time-tracker-icon" width="12" height="12" />
                                        <div class="time-tracker-time">01:31:25 AM</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Divider between time tracker and database -->
                            <div class="database-time-divider"></div>

                            <div class="database-stat database-status">
                                <div class="database-status-row" style="display: flex; align-items: center; gap: 12px;">
                                    <div style="display: flex; flex-direction: column;">
                                        <span class="database-label-row">
                                            <img class="database-icon" src="./assets/data-cell-ic.svg" alt="Database Icon" width="12" height="12" />
                                            <span class="database-label">Database</span>
                                        </span>
                                        <span class="database-updated-row">
                                            <img src="./assets/update-time-ic.svg" alt="Updated Icon" width="12" height="12" />
                                            <span class="database-updated-text">Updated 5 min ago</span>
                                        </span>
                                    </div>
                                    <button class="database-update-btn action-button">Update</button>
                                </div>
                            </div>
                        </div>
                        <div class="database-right" style="display: flex; align-items: center; gap: 16px;">
                            <div class="privacy-mode-container">
                                <span>Privacy: Off</span>
                            </div>
                            <div class="marketplace-dropdown-container">
                                <span>All Marketplaces</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function updateWidthIndicator() {
            const container = document.getElementById('testContainer');
            const widthValue = document.getElementById('widthValue');
            const width = container.offsetWidth;
            widthValue.textContent = width + 'px';
        }

        // Update width indicator on resize
        const resizeObserver = new ResizeObserver(updateWidthIndicator);
        resizeObserver.observe(document.getElementById('testContainer'));
        
        // Initial update
        updateWidthIndicator();
    </script>
</body>
</html>
