<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Date Change Manager Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .test-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #333;
        }
        
        .test-button {
            background: #007AFF;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 12px;
            margin-bottom: 8px;
        }
        
        .test-button:hover {
            background: #0056CC;
        }
        
        .test-button.danger {
            background: #FF3B30;
        }
        
        .test-button.danger:hover {
            background: #D70015;
        }
        
        .test-results {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 16px;
            margin-top: 16px;
            font-family: 'SF Mono', Monaco, monospace;
            font-size: 13px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .status-info {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 12px;
            margin: 12px 0;
            border-radius: 4px;
        }
        
        .status-success {
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
            padding: 12px;
            margin: 12px 0;
            border-radius: 4px;
        }
        
        .status-error {
            background: #ffebee;
            border-left: 4px solid #f44336;
            padding: 12px;
            margin: 12px 0;
            border-radius: 4px;
        }
        
        .current-status {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 20px;
        }
        
        .status-card {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 16px;
        }
        
        .status-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 4px;
        }
        
        .status-value {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
    </style>
</head>
<body>
    <h1>🕐 Date Change Manager Test Suite</h1>
    <p>Test the automatic date-based refresh functionality for the Snap Dashboard.</p>

    <!-- Current Status -->
    <div class="test-container">
        <div class="test-title">Current Status</div>
        <div class="current-status" id="current-status">
            <!-- Status cards will be populated by JavaScript -->
        </div>
        <button class="test-button" onclick="updateStatus()">Refresh Status</button>
    </div>

    <!-- Basic Functionality Tests -->
    <div class="test-container">
        <div class="test-title">1. Basic Functionality Tests</div>
        <button class="test-button" onclick="testManagerAvailability()">Test Manager Availability</button>
        <button class="test-button" onclick="testTimezoneUtility()">Test Timezone Utility</button>
        <button class="test-button" onclick="testDateDetection()">Test Date Detection</button>
        <button class="test-button" onclick="testCallbackRegistration()">Test Callback Registration</button>
        <div class="test-results" id="basic-test-results"></div>
    </div>

    <!-- Date Change Simulation -->
    <div class="test-container">
        <div class="test-title">2. Date Change Simulation</div>
        <button class="test-button" onclick="simulateDateChange()">Simulate Date Change</button>
        <button class="test-button" onclick="simulateMidnightTransition()">Simulate Midnight Transition</button>
        <button class="test-button" onclick="simulateDSTTransition()">Simulate DST Transition</button>
        <div class="test-results" id="simulation-test-results"></div>
    </div>

    <!-- Monitoring Tests -->
    <div class="test-container">
        <div class="test-title">3. Monitoring Tests</div>
        <button class="test-button" onclick="startMonitoring()">Start Monitoring</button>
        <button class="test-button" onclick="stopMonitoring()">Stop Monitoring</button>
        <button class="test-button" onclick="testAdaptiveTiming()">Test Adaptive Timing</button>
        <button class="test-button danger" onclick="forceCheck()">Force Date Check</button>
        <button class="test-button" onclick="performHealthCheck()">Health Check</button>
        <button class="test-button" onclick="showDiagnostics()">Show Diagnostics</button>
        <div class="test-results" id="monitoring-test-results"></div>
    </div>

    <!-- Performance Tests -->
    <div class="test-container">
        <div class="test-title">4. Performance & Error Handling</div>
        <button class="test-button" onclick="testMultipleCallbacks()">Test Multiple Callbacks</button>
        <button class="test-button" onclick="testErrorHandling()">Test Error Handling</button>
        <button class="test-button" onclick="testMemoryCleanup()">Test Memory Cleanup</button>
        <div class="test-results" id="performance-test-results"></div>
    </div>

    <!-- Load Required Scripts -->
    <script src="utils/timezone.js"></script>
    <script src="utils/date-change-manager.js"></script>
    <script src="performance-optimizations/event-cleanup-manager.js"></script>

    <script>
        // Test utilities
        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `status-${type}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            container.appendChild(resultDiv);
            container.scrollTop = container.scrollHeight;
        }

        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        // Status update function
        function updateStatus() {
            const statusContainer = document.getElementById('current-status');
            
            if (!window.DateChangeManager) {
                statusContainer.innerHTML = '<div class="status-error">DateChangeManager not available</div>';
                return;
            }

            const status = window.DateChangeManager.getStatus();
            const currentDate = window.SnapTimezone ? 
                window.SnapTimezone.getCurrentPacificDateString() : 
                new Date().toISOString().split('T')[0];

            statusContainer.innerHTML = `
                <div class="status-card">
                    <div class="status-label">Manager Status</div>
                    <div class="status-value">${status.isActive ? '🟢 Active' : '🔴 Inactive'}</div>
                </div>
                <div class="status-card">
                    <div class="status-label">Current Pacific Date</div>
                    <div class="status-value">${currentDate}</div>
                </div>
                <div class="status-card">
                    <div class="status-label">Last Known Date</div>
                    <div class="status-value">${status.lastKnownDate || 'Not set'}</div>
                </div>
                <div class="status-card">
                    <div class="status-label">Near Midnight</div>
                    <div class="status-value">${status.isNearMidnight ? '🌙 Yes' : '☀️ No'}</div>
                </div>
                <div class="status-card">
                    <div class="status-label">Registered Callbacks</div>
                    <div class="status-value">${status.callbackCount}</div>
                </div>
                <div class="status-card">
                    <div class="status-label">Consecutive Errors</div>
                    <div class="status-value">${status.consecutiveErrors}</div>
                </div>
            `;
        }

        // Basic functionality tests
        function testManagerAvailability() {
            clearResults('basic-test-results');
            
            if (window.DateChangeManager) {
                addResult('basic-test-results', 'DateChangeManager is available', 'success');
                addResult('basic-test-results', `Manager type: ${typeof window.DateChangeManager}`, 'info');
                
                const methods = ['startMonitoring', 'stopMonitoring', 'registerCallback', 'getStatus'];
                methods.forEach(method => {
                    if (typeof window.DateChangeManager[method] === 'function') {
                        addResult('basic-test-results', `✓ Method ${method} is available`, 'success');
                    } else {
                        addResult('basic-test-results', `✗ Method ${method} is missing`, 'error');
                    }
                });
            } else {
                addResult('basic-test-results', 'DateChangeManager is not available', 'error');
            }
        }

        function testTimezoneUtility() {
            clearResults('basic-test-results');
            
            if (window.SnapTimezone) {
                addResult('basic-test-results', 'SnapTimezone utility is available', 'success');
                
                try {
                    const pacificTime = window.SnapTimezone.getPacificTime();
                    addResult('basic-test-results', `Pacific Time: ${pacificTime.toString()}`, 'info');
                    
                    const pacificDate = window.SnapTimezone.getPacificDate();
                    addResult('basic-test-results', `Pacific Date: ${pacificDate.toString()}`, 'info');
                    
                    const dateString = window.DateChangeManager.getCurrentPacificDateString();
                    addResult('basic-test-results', `Date String: ${dateString}`, 'info');
                    
                    addResult('basic-test-results', 'Timezone utility working correctly', 'success');
                } catch (error) {
                    addResult('basic-test-results', `Timezone utility error: ${error.message}`, 'error');
                }
            } else {
                addResult('basic-test-results', 'SnapTimezone utility is not available', 'error');
            }
        }

        function testDateDetection() {
            clearResults('basic-test-results');
            
            if (!window.DateChangeManager) {
                addResult('basic-test-results', 'DateChangeManager not available', 'error');
                return;
            }

            try {
                // Test current date detection
                const currentDate = window.DateChangeManager.getCurrentPacificDateString();
                addResult('basic-test-results', `Current date detected: ${currentDate}`, 'info');
                
                // Test midnight proximity detection
                const isNearMidnight = window.DateChangeManager.isNearMidnightPacific();
                addResult('basic-test-results', `Near midnight: ${isNearMidnight}`, 'info');
                
                // Test date change check
                window.DateChangeManager.forceCheck();
                addResult('basic-test-results', 'Force check completed successfully', 'success');
                
            } catch (error) {
                addResult('basic-test-results', `Date detection error: ${error.message}`, 'error');
            }
        }

        function testCallbackRegistration() {
            clearResults('basic-test-results');
            
            if (!window.DateChangeManager) {
                addResult('basic-test-results', 'DateChangeManager not available', 'error');
                return;
            }

            try {
                let callbackExecuted = false;
                
                // Register a test callback
                const testCallback = (prevDate, currDate) => {
                    callbackExecuted = true;
                    addResult('basic-test-results', `Test callback executed: ${prevDate} → ${currDate}`, 'success');
                };
                
                window.DateChangeManager.registerCallback(testCallback, 10);
                addResult('basic-test-results', 'Test callback registered successfully', 'success');
                
                // Simulate a date change to test the callback
                window.DateChangeManager.simulateDateChange('2025-08-02');
                
                setTimeout(() => {
                    if (callbackExecuted) {
                        addResult('basic-test-results', 'Callback execution test passed', 'success');
                    } else {
                        addResult('basic-test-results', 'Callback was not executed', 'error');
                    }
                    
                    // Unregister the test callback
                    window.DateChangeManager.unregisterCallback(testCallback);
                    addResult('basic-test-results', 'Test callback unregistered', 'info');
                }, 100);
                
            } catch (error) {
                addResult('basic-test-results', `Callback registration error: ${error.message}`, 'error');
            }
        }

        // Simulation tests
        function simulateDateChange() {
            clearResults('simulation-test-results');
            
            if (!window.DateChangeManager) {
                addResult('simulation-test-results', 'DateChangeManager not available', 'error');
                return;
            }

            const today = new Date();
            const tomorrow = new Date(today);
            tomorrow.setDate(today.getDate() + 1);
            const tomorrowString = tomorrow.toISOString().split('T')[0];
            
            addResult('simulation-test-results', `Simulating date change to: ${tomorrowString}`, 'info');
            window.DateChangeManager.simulateDateChange(tomorrowString);
            addResult('simulation-test-results', 'Date change simulation completed', 'success');
        }

        function simulateMidnightTransition() {
            clearResults('simulation-test-results');
            addResult('simulation-test-results', 'Simulating midnight transition (Aug 1 → Aug 2)', 'info');
            
            // Simulate the exact scenario from the requirements
            window.DateChangeManager.simulateDateChange('2025-08-02');
            addResult('simulation-test-results', 'Midnight transition simulation completed', 'success');
        }

        function simulateDSTTransition() {
            clearResults('simulation-test-results');
            addResult('simulation-test-results', 'Simulating DST transition scenario', 'info');
            
            // Simulate a date change during DST transition
            const dstDate = '2025-03-10'; // Spring forward date
            window.DateChangeManager.simulateDateChange(dstDate);
            addResult('simulation-test-results', `DST transition simulation completed: ${dstDate}`, 'success');
        }

        // Monitoring tests
        function startMonitoring() {
            clearResults('monitoring-test-results');
            
            if (!window.DateChangeManager) {
                addResult('monitoring-test-results', 'DateChangeManager not available', 'error');
                return;
            }

            try {
                window.DateChangeManager.startMonitoring();
                addResult('monitoring-test-results', 'Monitoring started successfully', 'success');
                updateStatus();
            } catch (error) {
                addResult('monitoring-test-results', `Error starting monitoring: ${error.message}`, 'error');
            }
        }

        function stopMonitoring() {
            clearResults('monitoring-test-results');
            
            if (!window.DateChangeManager) {
                addResult('monitoring-test-results', 'DateChangeManager not available', 'error');
                return;
            }

            try {
                window.DateChangeManager.stopMonitoring();
                addResult('monitoring-test-results', 'Monitoring stopped successfully', 'success');
                updateStatus();
            } catch (error) {
                addResult('monitoring-test-results', `Error stopping monitoring: ${error.message}`, 'error');
            }
        }

        function testAdaptiveTiming() {
            clearResults('monitoring-test-results');
            addResult('monitoring-test-results', 'Testing adaptive timing logic...', 'info');
            
            const isNearMidnight = window.DateChangeManager.isNearMidnightPacific();
            addResult('monitoring-test-results', `Currently near midnight: ${isNearMidnight}`, 'info');
            
            if (isNearMidnight) {
                addResult('monitoring-test-results', 'Using frequent checks (30 seconds)', 'info');
            } else {
                addResult('monitoring-test-results', 'Using normal checks (5 minutes)', 'info');
            }
            
            addResult('monitoring-test-results', 'Adaptive timing test completed', 'success');
        }

        function forceCheck() {
            clearResults('monitoring-test-results');

            if (!window.DateChangeManager) {
                addResult('monitoring-test-results', 'DateChangeManager not available', 'error');
                return;
            }

            try {
                addResult('monitoring-test-results', 'Forcing date change check...', 'info');
                window.DateChangeManager.forceCheck();
                addResult('monitoring-test-results', 'Force check completed', 'success');
                updateStatus();
            } catch (error) {
                addResult('monitoring-test-results', `Force check error: ${error.message}`, 'error');
            }
        }

        function performHealthCheck() {
            clearResults('monitoring-test-results');

            if (!window.checkDateChangeManagerHealth) {
                addResult('monitoring-test-results', 'Health check function not available', 'error');
                return;
            }

            try {
                addResult('monitoring-test-results', 'Performing health check...', 'info');
                window.checkDateChangeManagerHealth();
                addResult('monitoring-test-results', 'Health check completed - see console for details', 'success');
                updateStatus();
            } catch (error) {
                addResult('monitoring-test-results', `Health check error: ${error.message}`, 'error');
            }
        }

        function showDiagnostics() {
            clearResults('monitoring-test-results');

            if (!window.getDateChangeManagerDiagnostics) {
                addResult('monitoring-test-results', 'Diagnostics function not available', 'error');
                return;
            }

            try {
                const diagnostics = window.getDateChangeManagerDiagnostics();
                if (diagnostics) {
                    addResult('monitoring-test-results', 'Diagnostics retrieved successfully:', 'success');
                    addResult('monitoring-test-results', `<pre>${JSON.stringify(diagnostics, null, 2)}</pre>`, 'info');
                } else {
                    addResult('monitoring-test-results', 'No diagnostics available', 'error');
                }
                updateStatus();
            } catch (error) {
                addResult('monitoring-test-results', `Diagnostics error: ${error.message}`, 'error');
            }
        }

        // Performance tests
        function testMultipleCallbacks() {
            clearResults('performance-test-results');
            
            if (!window.DateChangeManager) {
                addResult('performance-test-results', 'DateChangeManager not available', 'error');
                return;
            }

            const callbacks = [];
            const callbackCount = 5;
            
            // Register multiple callbacks with different priorities
            for (let i = 0; i < callbackCount; i++) {
                const callback = (prevDate, currDate) => {
                    addResult('performance-test-results', `Callback ${i + 1} executed (priority ${i + 1})`, 'info');
                };
                callbacks.push(callback);
                window.DateChangeManager.registerCallback(callback, i + 1);
            }
            
            addResult('performance-test-results', `Registered ${callbackCount} test callbacks`, 'info');
            
            // Trigger callbacks
            window.DateChangeManager.simulateDateChange('2025-08-03');
            
            // Cleanup
            setTimeout(() => {
                callbacks.forEach(callback => {
                    window.DateChangeManager.unregisterCallback(callback);
                });
                addResult('performance-test-results', 'All test callbacks cleaned up', 'success');
            }, 200);
        }

        function testErrorHandling() {
            clearResults('performance-test-results');
            
            if (!window.DateChangeManager) {
                addResult('performance-test-results', 'DateChangeManager not available', 'error');
                return;
            }

            // Register a callback that throws an error
            const errorCallback = () => {
                throw new Error('Test error in callback');
            };
            
            window.DateChangeManager.registerCallback(errorCallback, 1);
            addResult('performance-test-results', 'Registered error-throwing callback', 'info');
            
            // Trigger the error
            window.DateChangeManager.simulateDateChange('2025-08-04');
            
            setTimeout(() => {
                addResult('performance-test-results', 'Error handling test completed - system should remain stable', 'success');
                window.DateChangeManager.unregisterCallback(errorCallback);
            }, 100);
        }

        function testMemoryCleanup() {
            clearResults('performance-test-results');
            addResult('performance-test-results', 'Testing memory cleanup...', 'info');
            
            const initialStatus = window.DateChangeManager.getStatus();
            addResult('performance-test-results', `Initial callback count: ${initialStatus.callbackCount}`, 'info');
            
            // Register and unregister callbacks
            const tempCallbacks = [];
            for (let i = 0; i < 10; i++) {
                const callback = () => {};
                tempCallbacks.push(callback);
                window.DateChangeManager.registerCallback(callback, 5);
            }
            
            const midStatus = window.DateChangeManager.getStatus();
            addResult('performance-test-results', `After registration: ${midStatus.callbackCount} callbacks`, 'info');
            
            // Unregister all
            tempCallbacks.forEach(callback => {
                window.DateChangeManager.unregisterCallback(callback);
            });
            
            const finalStatus = window.DateChangeManager.getStatus();
            addResult('performance-test-results', `After cleanup: ${finalStatus.callbackCount} callbacks`, 'info');
            
            if (finalStatus.callbackCount === initialStatus.callbackCount) {
                addResult('performance-test-results', 'Memory cleanup test passed', 'success');
            } else {
                addResult('performance-test-results', 'Memory cleanup test failed - callbacks not properly removed', 'error');
            }
        }

        // Initialize on page load
        window.addEventListener('load', () => {
            updateStatus();
            
            // Auto-refresh status every 10 seconds
            setInterval(updateStatus, 10000);
        });
    </script>
</body>
</html>
