<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auto-Refresh Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .test-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #333;
        }
        
        .test-button {
            background: #007AFF;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 12px;
            margin-bottom: 8px;
        }
        
        .test-button:hover {
            background: #0056CC;
        }
        
        .test-button.danger {
            background: #FF3B30;
        }
        
        .test-button.danger:hover {
            background: #D70015;
        }
        
        .test-results {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 16px;
            margin-top: 16px;
            font-family: 'SF Mono', Monaco, monospace;
            font-size: 13px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .status-info {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 12px;
            margin: 12px 0;
            border-radius: 4px;
        }
        
        .status-success {
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
            padding: 12px;
            margin: 12px 0;
            border-radius: 4px;
        }
        
        .status-error {
            background: #ffebee;
            border-left: 4px solid #f44336;
            padding: 12px;
            margin: 12px 0;
            border-radius: 4px;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 20px;
        }
        
        .status-card {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 16px;
        }
        
        .status-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 4px;
        }
        
        .status-value {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
    </style>
</head>
<body>
    <h1>🔄 Auto-Refresh Test Suite</h1>
    <p>Test the automatic date-based refresh functionality and debug any issues.</p>

    <!-- Current Status -->
    <div class="test-container">
        <div class="test-title">Current Status</div>
        <div class="status-grid" id="status-grid">
            <!-- Status cards will be populated by JavaScript -->
        </div>
        <button class="test-button" onclick="updateStatus()">Refresh Status</button>
    </div>

    <!-- DateChangeManager Tests -->
    <div class="test-container">
        <div class="test-title">DateChangeManager Tests</div>
        <button class="test-button" onclick="testManagerStatus()">Check Manager Status</button>
        <button class="test-button" onclick="testCallbackRegistration()">Test Callback Registration</button>
        <button class="test-button" onclick="simulateDateChange()">Simulate Date Change</button>
        <button class="test-button" onclick="simulateTabHidden()">Simulate Tab Hidden</button>
        <button class="test-button danger" onclick="forceCheck()">Force Check</button>
        <div class="test-results" id="manager-test-results"></div>
    </div>

    <!-- Debug Console -->
    <div class="test-container">
        <div class="test-title">Debug Console</div>
        <button class="test-button" onclick="clearConsole()">Clear Console</button>
        <button class="test-button" onclick="enableDebugMode()">Enable Debug Mode</button>
        <button class="test-button" onclick="testTimezoneUtility()">Test Timezone Utility</button>
        <button class="test-button" onclick="testPerformanceOptimizations()">Test Performance</button>
        <button class="test-button" onclick="validateTimezoneAccuracy()">Validate Timezone</button>
        <div class="test-results" id="debug-console"></div>
    </div>

    <!-- Load Required Scripts -->
    <script src="utils/timezone.js"></script>
    <script src="performance-optimizations/event-cleanup-manager.js"></script>
    <script src="performance-optimizations/unified-timer-manager.js"></script>
    <script src="utils/date-change-manager.js"></script>

    <script>
        let debugMode = false;
        
        // Override console methods to capture output
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;
        
        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `status-${type}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            container.appendChild(resultDiv);
            container.scrollTop = container.scrollHeight;
        }

        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        function updateStatus() {
            const statusGrid = document.getElementById('status-grid');
            
            // Check DateChangeManager availability
            const managerAvailable = !!window.DateChangeManager;
            const managerStatus = managerAvailable ? window.DateChangeManager.getStatus() : null;
            
            // Check timezone utility
            const timezoneAvailable = !!window.SnapTimezone;
            const currentPacificDate = timezoneAvailable ? 
                window.SnapTimezone.getCurrentPacificDateString() : 'N/A';
            
            // Check EventCleanupManager
            const eventManagerAvailable = !!window.EventCleanupManager;
            
            statusGrid.innerHTML = `
                <div class="status-card">
                    <div class="status-label">DateChangeManager</div>
                    <div class="status-value">${managerAvailable ? '✅ Available' : '❌ Missing'}</div>
                </div>
                <div class="status-card">
                    <div class="status-label">Manager Active</div>
                    <div class="status-value">${managerStatus ? (managerStatus.isActive ? '🟢 Active' : '🔴 Inactive') : 'N/A'}</div>
                </div>
                <div class="status-card">
                    <div class="status-label">Timezone Utility</div>
                    <div class="status-value">${timezoneAvailable ? '✅ Available' : '❌ Missing'}</div>
                </div>
                <div class="status-card">
                    <div class="status-label">Current Pacific Date</div>
                    <div class="status-value">${currentPacificDate}</div>
                </div>
                <div class="status-card">
                    <div class="status-label">Event Manager</div>
                    <div class="status-value">${eventManagerAvailable ? '✅ Available' : '❌ Missing'}</div>
                </div>
                <div class="status-card">
                    <div class="status-label">Registered Callbacks</div>
                    <div class="status-value">${managerStatus ? managerStatus.callbackCount : 'N/A'}</div>
                </div>
                <div class="status-card">
                    <div class="status-label">Tab Visibility</div>
                    <div class="status-value">${managerStatus ? (managerStatus.isTabVisible ? '👁️ Visible' : '🙈 Hidden') : 'N/A'}</div>
                </div>
                <div class="status-card">
                    <div class="status-label">Missed Checks</div>
                    <div class="status-value">${managerStatus ? managerStatus.missedChecksWhileHidden : 'N/A'}</div>
                </div>
            `;
        }

        function testManagerStatus() {
            clearResults('manager-test-results');
            
            if (!window.DateChangeManager) {
                addResult('manager-test-results', 'DateChangeManager not available', 'error');
                return;
            }

            const status = window.DateChangeManager.getStatus();
            addResult('manager-test-results', `Manager Status: ${JSON.stringify(status, null, 2)}`, 'info');
            
            if (!status.isActive) {
                addResult('manager-test-results', 'Manager is not active - starting monitoring...', 'info');
                window.DateChangeManager.startMonitoring();
                
                setTimeout(() => {
                    const newStatus = window.DateChangeManager.getStatus();
                    addResult('manager-test-results', `New Status: Active = ${newStatus.isActive}`, 
                        newStatus.isActive ? 'success' : 'error');
                }, 1000);
            } else {
                addResult('manager-test-results', 'Manager is already active', 'success');
            }
        }

        function testCallbackRegistration() {
            clearResults('manager-test-results');
            
            if (!window.DateChangeManager) {
                addResult('manager-test-results', 'DateChangeManager not available', 'error');
                return;
            }

            let callbackExecuted = false;
            const testCallback = (prevDate, currDate) => {
                callbackExecuted = true;
                addResult('manager-test-results', `Test callback executed: ${prevDate} → ${currDate}`, 'success');
            };
            
            window.DateChangeManager.registerCallback(testCallback, 10);
            addResult('manager-test-results', 'Test callback registered', 'info');
            
            // Simulate date change
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            const tomorrowString = tomorrow.toISOString().split('T')[0];
            
            window.DateChangeManager.simulateDateChange(tomorrowString);
            
            setTimeout(() => {
                if (callbackExecuted) {
                    addResult('manager-test-results', 'Callback registration test PASSED', 'success');
                } else {
                    addResult('manager-test-results', 'Callback registration test FAILED', 'error');
                }
                
                window.DateChangeManager.unregisterCallback(testCallback);
                addResult('manager-test-results', 'Test callback unregistered', 'info');
            }, 500);
        }

        function simulateDateChange() {
            clearResults('manager-test-results');
            
            if (!window.DateChangeManager) {
                addResult('manager-test-results', 'DateChangeManager not available', 'error');
                return;
            }

            const today = new Date();
            const tomorrow = new Date(today);
            tomorrow.setDate(today.getDate() + 1);
            const tomorrowString = tomorrow.toISOString().split('T')[0];
            
            addResult('manager-test-results', `Simulating date change to: ${tomorrowString}`, 'info');
            window.DateChangeManager.simulateDateChange(tomorrowString);
            addResult('manager-test-results', 'Date change simulation completed', 'success');
        }

        function simulateTabHidden() {
            clearResults('manager-test-results');

            if (!window.DateChangeManager) {
                addResult('manager-test-results', 'DateChangeManager not available', 'error');
                return;
            }

            addResult('manager-test-results', 'Simulating tab becoming hidden...', 'info');

            // Simulate tab becoming hidden
            Object.defineProperty(document, 'hidden', {
                writable: true,
                value: true
            });

            // Trigger visibility change event
            const event = new Event('visibilitychange');
            document.dispatchEvent(event);

            addResult('manager-test-results', 'Tab is now simulated as hidden', 'info');

            // After 3 seconds, simulate tab becoming visible again
            setTimeout(() => {
                addResult('manager-test-results', 'Simulating tab becoming visible...', 'info');

                Object.defineProperty(document, 'hidden', {
                    writable: true,
                    value: false
                });

                const visibleEvent = new Event('visibilitychange');
                document.dispatchEvent(visibleEvent);

                addResult('manager-test-results', 'Tab is now simulated as visible', 'success');
            }, 3000);
        }

        function forceCheck() {
            clearResults('manager-test-results');

            if (!window.DateChangeManager) {
                addResult('manager-test-results', 'DateChangeManager not available', 'error');
                return;
            }

            addResult('manager-test-results', 'Forcing date change check...', 'info');
            window.DateChangeManager.forceCheck();
            addResult('manager-test-results', 'Force check completed', 'success');
        }

        function clearConsole() {
            clearResults('debug-console');
        }

        function enableDebugMode() {
            debugMode = !debugMode;
            
            if (debugMode) {
                // Capture console output
                console.log = function(...args) {
                    originalConsoleLog.apply(console, args);
                    addResult('debug-console', args.join(' '), 'info');
                };
                
                console.error = function(...args) {
                    originalConsoleError.apply(console, args);
                    addResult('debug-console', args.join(' '), 'error');
                };
                
                console.warn = function(...args) {
                    originalConsoleWarn.apply(console, args);
                    addResult('debug-console', args.join(' '), 'error');
                };
                
                addResult('debug-console', 'Debug mode ENABLED - capturing console output', 'success');
            } else {
                // Restore original console methods
                console.log = originalConsoleLog;
                console.error = originalConsoleError;
                console.warn = originalConsoleWarn;
                
                addResult('debug-console', 'Debug mode DISABLED', 'info');
            }
        }

        function testTimezoneUtility() {
            clearResults('debug-console');

            if (!window.SnapTimezone) {
                addResult('debug-console', 'SnapTimezone utility not available', 'error');
                return;
            }

            try {
                const pacificTime = window.SnapTimezone.getPacificTime();
                const pacificDate = window.SnapTimezone.getPacificDate();
                const dateString = window.SnapTimezone.getCurrentPacificDateString();

                addResult('debug-console', `Pacific Time: ${pacificTime.toString()}`, 'info');
                addResult('debug-console', `Pacific Date: ${pacificDate.toString()}`, 'info');
                addResult('debug-console', `Date String: ${dateString}`, 'info');
                addResult('debug-console', 'Timezone utility working correctly', 'success');
            } catch (error) {
                addResult('debug-console', `Timezone utility error: ${error.message}`, 'error');
            }
        }

        function testPerformanceOptimizations() {
            clearResults('debug-console');

            // Test UnifiedTimerManager
            if (!window.UnifiedTimerManager) {
                addResult('debug-console', 'UnifiedTimerManager not available', 'error');
                return;
            }

            addResult('debug-console', 'Testing UnifiedTimerManager performance...', 'info');

            // Get initial stats
            const initialStats = window.UnifiedTimerManager.getStats();
            addResult('debug-console', `Initial stats: ${JSON.stringify(initialStats)}`, 'info');

            // Register test callbacks
            let testCallbackCount = 0;
            const testCallback1 = () => testCallbackCount++;
            const testCallback2 = () => testCallbackCount += 2;

            const id1 = window.UnifiedTimerManager.registerCallback(testCallback1, 1000, 'test1');
            const id2 = window.UnifiedTimerManager.registerCallback(testCallback2, 2000, 'test2');

            addResult('debug-console', `Registered test callbacks (IDs: ${id1}, ${id2})`, 'info');

            // Start if not running
            if (!window.UnifiedTimerManager.isActive) {
                window.UnifiedTimerManager.start();
                addResult('debug-console', 'Started UnifiedTimerManager', 'info');
            }

            // Test for 5 seconds
            setTimeout(() => {
                const finalStats = window.UnifiedTimerManager.getStats();
                const callbackInfo = window.UnifiedTimerManager.getCallbackInfo();

                addResult('debug-console', `Final stats: ${JSON.stringify(finalStats)}`, 'info');
                addResult('debug-console', `Callback info: ${JSON.stringify(callbackInfo)}`, 'info');
                addResult('debug-console', `Test callback executed ${testCallbackCount} times`, 'info');

                // Cleanup
                window.UnifiedTimerManager.unregisterCallback(id1);
                window.UnifiedTimerManager.unregisterCallback(id2);

                addResult('debug-console', 'Performance test completed successfully', 'success');
            }, 5000);
        }

        function validateTimezoneAccuracy() {
            clearResults('debug-console');

            if (!window.SnapTimezone || !window.SnapTimezone.validateTimezoneAccuracy) {
                addResult('debug-console', 'Timezone validation not available', 'error');
                return;
            }

            addResult('debug-console', 'Validating timezone accuracy...', 'info');

            const validation = window.SnapTimezone.validateTimezoneAccuracy();

            if (validation.isAccurate) {
                addResult('debug-console', '✅ Timezone calculations are accurate', 'success');
                addResult('debug-console', `Our method: ${validation.ourMethod}`, 'info');
                addResult('debug-console', `Direct method: ${validation.directMethod}`, 'info');
                addResult('debug-console', `Differences: ${JSON.stringify(validation.differences)}ms`, 'info');
            } else {
                addResult('debug-console', '❌ Timezone accuracy validation failed', 'error');
                if (validation.error) {
                    addResult('debug-console', `Error: ${validation.error}`, 'error');
                } else {
                    addResult('debug-console', `Validation details: ${JSON.stringify(validation)}`, 'error');
                }
            }
        }

        // Initialize on page load
        window.addEventListener('load', () => {
            updateStatus();
            
            // Auto-refresh status every 5 seconds
            setInterval(updateStatus, 5000);
            
            // Enable debug mode by default
            enableDebugMode();
        });
    </script>
</body>
</html>
