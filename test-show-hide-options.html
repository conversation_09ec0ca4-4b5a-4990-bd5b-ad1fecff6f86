<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Show/Hide Options Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-card {
            background: white;
            border: 1.5px solid #E9EBF2;
            border-radius: 14px;
            padding: 24px;
            margin: 20px 0;
            max-width: 800px;
        }
        
        .Sales-title-date-div {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
        }
        
        .title-date-section {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .controls-section {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .show-hide-options-div {
            display: flex;
            align-items: center;
            position: relative;
        }
        
        .show-hide-options-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            background: #F7F8FA;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .show-hide-options-btn:hover {
            background: #E5E7EB;
        }
        
        .show-hide-options-btn.active {
            background: #470CED !important;
        }
        
        .show-hide-options-btn.active img {
            filter: brightness(0) invert(1);
        }
        
        .show-hide-options-dropdown {
            position: absolute;
            top: calc(100% + 20px);
            left: 50%;
            transform: translateX(-50%);
            width: auto;
            min-width: 120px;
            background: #FFFFFF;
            border: 1.5px solid #E9EBF2;
            border-radius: 8px;
            z-index: 9999;
            display: none;
            box-sizing: border-box;
            opacity: 0;
            transform: translateX(-50%) translateY(-10px);
            transition: all 0.2s ease;
        }
        
        .show-hide-options-dropdown.show {
            display: block;
            opacity: 1;
            transform: translateX(-50%) translateY(0);
        }
        
        .show-hide-options-dropdown-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
            font-size: 12px;
        }
        
        .show-hide-options-dropdown-item:hover {
            background: #F3F4F6;
        }
        
        .show-hide-options-checkbox {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 20px;
            height: 20px;
            flex-shrink: 0;
        }
        
        .show-hide-options-dropdown-text {
            font-family: 'Amazon Ember', sans-serif;
            font-weight: 500;
            font-size: 12px;
            line-height: 1.2;
            color: #606F95;
            flex: 1;
        }
        
        .chart-placeholder {
            width: 100%;
            height: 200px;
            background: #f0f0f0;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <h1>Show/Hide Options Test</h1>
    
    <!-- Test Card 1: Last Week's Sales -->
    <div class="test-card">
        <div class="Sales-title-date-div">
            <div class="title-date-section">
                <img src="./assets/analytics-sales-ic.svg" alt="Sales Analytics Icon" width="16" height="16" />
                <span>Last Week's Sales</span>
            </div>
            <div class="controls-section">
                <div class="show-hide-options-div">
                    <div class="show-hide-options-btn" data-tooltip="Show/hide Options">
                        <img src="./assets/show-hide-options-ic.svg" alt="Show/hide Options" width="16" height="16" />
                    </div>
                    <div class="show-hide-options-dropdown" id="test-dropdown">
                        <div class="show-hide-options-dropdown-item" data-option="returns">
                            <div class="show-hide-options-checkbox">
                                <img src="./assets/checkbox-ic.svg" alt="Checkbox" width="20" height="20" />
                            </div>
                            <span class="show-hide-options-dropdown-text">Show Returns</span>
                        </div>
                        <div class="show-hide-options-dropdown-item" data-option="royalties">
                            <div class="show-hide-options-checkbox">
                                <img src="./assets/checkbox-ic.svg" alt="Checkbox" width="20" height="20" />
                            </div>
                            <span class="show-hide-options-dropdown-text">Show Royalties</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="chart-placeholder">Chart would be here</div>
    </div>

    <script>
        // Simple test functionality
        document.addEventListener('DOMContentLoaded', function() {
            const btn = document.querySelector('.show-hide-options-btn');
            const dropdown = document.querySelector('.show-hide-options-dropdown');
            const items = document.querySelectorAll('.show-hide-options-dropdown-item');
            
            let isOpen = false;
            
            btn.addEventListener('click', function(e) {
                e.stopPropagation();
                isOpen = !isOpen;
                
                if (isOpen) {
                    dropdown.classList.add('show');
                    btn.classList.add('active');
                } else {
                    dropdown.classList.remove('show');
                    btn.classList.remove('active');
                }
            });
            
            items.forEach(item => {
                item.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const checkbox = item.querySelector('.show-hide-options-checkbox img');
                    const isChecked = checkbox.src.includes('checkbox-ic.svg');
                    
                    checkbox.src = isChecked ? './assets/uncheckedbox-ic.svg' : './assets/checkbox-ic.svg';
                    
                    console.log('Toggled:', item.getAttribute('data-option'), !isChecked);
                });
            });
            
            document.addEventListener('click', function() {
                if (isOpen) {
                    dropdown.classList.remove('show');
                    btn.classList.remove('active');
                    isOpen = false;
                }
            });
        });
    </script>
</body>
</html>
