<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Responsive Header Test - 32px Gap Threshold</title>
    <link rel="stylesheet" href="snapapp.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Amazon Ember', Arial, sans-serif;
            background: var(--bg-primary, #f8f9fa);
        }
        
        /* Test container to simulate different widths */
        .test-container {
            border: 2px solid #007bff;
            margin: 20px;
            resize: horizontal;
            overflow: auto;
            min-width: 600px;
            max-width: 100%;
            width: 1400px;
            background: white;
        }
        
        .width-indicator {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #007bff;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
            z-index: 10000;
        }
        
        .gap-indicator {
            position: fixed;
            top: 50px;
            right: 10px;
            background: #28a745;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
            z-index: 10000;
        }
        
        .layout-indicator {
            position: fixed;
            top: 90px;
            right: 10px;
            background: #dc3545;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
            z-index: 10000;
        }
        
        /* Override fixed positioning for test */
        .dashboard-header {
            position: relative !important;
            left: 0 !important;
            right: auto !important;
            top: 0 !important;
        }
        
        /* Visual indicators for database elements */
        .database-left {
            background: rgba(0, 123, 255, 0.1);
            border: 1px dashed #007bff;
            border-radius: 4px;
            padding: 8px;
        }
        
        .database-right {
            background: rgba(40, 167, 69, 0.1);
            border: 1px dashed #28a745;
            border-radius: 4px;
            padding: 8px;
        }
        
        .instructions {
            margin: 20px;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="width-indicator" id="widthIndicator">Width: <span id="widthValue">0px</span></div>
    <div class="gap-indicator" id="gapIndicator">Gap: <span id="gapValue">0px</span></div>
    <div class="layout-indicator" id="layoutIndicator">Layout: <span id="layoutValue">horizontal</span></div>
    
    <div class="instructions">
        <h2>Responsive Header Test - Pure CSS Solution with 32px Gap Threshold</h2>
        <p><strong>Instructions:</strong> Resize the blue container by dragging its right edge. Watch how the layout switches from horizontal to vertical using pure CSS container queries when the container width reaches 972px (the point where the gap would be less than 32px).</p>
        <ul>
            <li><strong>Blue section:</strong> database-left (contains time tracker and database status)</li>
            <li><strong>Green section:</strong> database-right (contains privacy mode and marketplace dropdown)</li>
            <li><strong>CSS Breakpoint:</strong> 972px container width (calculated to maintain 32px minimum gap)</li>
            <li><strong>Technology:</strong> Pure CSS using container queries + media query fallback</li>
        </ul>
        <p><strong>Calculation:</strong> h1(200px) + gap(40px) + database-left(500px) + min-gap(32px) + database-right(200px) = 972px minimum</p>
    </div>
    
    <div class="test-container" id="testContainer">
        <div class="dashboard-header">
            <div class="dashboard-header-content">
                <h1>Dashboard</h1>
                <div class="database-container">
                    <div class="database-row">
                        <div class="database-left">
                            <!-- Time Tracker Components -->
                            <div class="time-tracker-container">
                                <!-- Local Time Block -->
                                <div class="time-tracker-block">
                                    <span class="time-tracker-label">Local Time</span>
                                    <div class="time-tracker-content">
                                        <img src="./assets/timezone-ic.svg" alt="Timezone Icon" class="time-tracker-icon" width="12" height="12" />
                                        <div class="time-tracker-time">11:31:25 AM</div>
                                    </div>
                                </div>

                                <!-- Divider between time trackers -->
                                <div class="time-tracker-divider"></div>

                                <!-- Pacific Time Block -->
                                <div class="time-tracker-block">
                                    <span class="time-tracker-label">Pacific Time</span>
                                    <div class="time-tracker-content">
                                        <img src="./assets/timezone-ic.svg" alt="Timezone Icon" class="time-tracker-icon" width="12" height="12" />
                                        <div class="time-tracker-time">01:31:25 AM</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Divider between time tracker and database -->
                            <div class="database-time-divider"></div>

                            <div class="database-stat database-status">
                                <div class="database-status-row" style="display: flex; align-items: center; gap: 12px;">
                                    <div style="display: flex; flex-direction: column;">
                                        <span class="database-label-row">
                                            <img class="database-icon" src="./assets/data-cell-ic.svg" alt="Database Icon" width="12" height="12" />
                                            <span class="database-label">Database</span>
                                        </span>
                                        <span class="database-updated-row">
                                            <img src="./assets/update-time-ic.svg" alt="Updated Icon" width="12" height="12" />
                                            <span class="database-updated-text">Updated 5 min ago</span>
                                        </span>
                                    </div>
                                    <button class="database-update-btn action-button">Update</button>
                                </div>
                            </div>
                        </div>
                        <div class="database-right">
                            <div class="privacy-mode-container">
                                <span>Privacy: Off</span>
                            </div>
                            <div class="marketplace-dropdown-container">
                                <span>All Marketplaces</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function updateIndicators() {
            const container = document.getElementById('testContainer');
            const widthValue = document.getElementById('widthValue');
            const gapValue = document.getElementById('gapValue');
            const layoutValue = document.getElementById('layoutValue');

            const width = container.offsetWidth;
            widthValue.textContent = width + 'px';

            // Calculate gap between database-left and database-right
            const databaseLeft = container.querySelector('.database-left');
            const databaseRight = container.querySelector('.database-right');

            if (databaseLeft && databaseRight) {
                const leftRect = databaseLeft.getBoundingClientRect();
                const rightRect = databaseRight.getBoundingClientRect();
                const gap = rightRect.left - (leftRect.left + leftRect.width);
                gapValue.textContent = Math.round(gap) + 'px';

                // Update gap indicator color based on threshold
                const gapIndicator = document.getElementById('gapIndicator');
                if (gap >= 32) {
                    gapIndicator.style.background = '#28a745'; // Green - good
                } else {
                    gapIndicator.style.background = '#dc3545'; // Red - below threshold
                }
            }

            // Detect layout based on CSS (pure CSS solution)
            const headerContent = container.querySelector('.dashboard-header-content');
            if (headerContent) {
                const computedStyle = window.getComputedStyle(headerContent);
                const flexDirection = computedStyle.flexDirection;
                const currentLayout = flexDirection === 'column' ? 'vertical' : 'horizontal';

                layoutValue.textContent = currentLayout;

                const layoutIndicator = document.getElementById('layoutIndicator');
                if (currentLayout === 'horizontal') {
                    layoutIndicator.style.background = '#28a745'; // Green
                } else {
                    layoutIndicator.style.background = '#dc3545'; // Red
                }
            }
        }

        // Update indicators on resize
        const resizeObserver = new ResizeObserver(updateIndicators);
        resizeObserver.observe(document.getElementById('testContainer'));

        // Also update on window resize (for gap calculations)
        window.addEventListener('resize', updateIndicators);

        // Initial update
        setTimeout(updateIndicators, 100); // Small delay to ensure elements are rendered
    </script>
</body>
</html>
