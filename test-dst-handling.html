<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DST Handling Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .test-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #333;
        }
        
        .test-button {
            background: #007AFF;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 12px;
            margin-bottom: 8px;
        }
        
        .test-button:hover {
            background: #0056CC;
        }
        
        .test-results {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 16px;
            margin-top: 16px;
            font-family: 'SF Mono', Monaco, monospace;
            font-size: 13px;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .status-success {
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
            padding: 12px;
            margin: 8px 0;
            border-radius: 4px;
        }
        
        .status-info {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 12px;
            margin: 8px 0;
            border-radius: 4px;
        }
        
        .status-warning {
            background: #fff3e0;
            border-left: 4px solid #ff9800;
            padding: 12px;
            margin: 8px 0;
            border-radius: 4px;
        }
        
        .dst-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 16px;
        }
        
        .dst-table th,
        .dst-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            font-size: 12px;
        }
        
        .dst-table th {
            background-color: #f5f5f5;
            font-weight: 600;
        }
        
        .dst-spring {
            background-color: #e8f5e8;
        }
        
        .dst-fall {
            background-color: #fff3e0;
        }
    </style>
</head>
<body>
    <h1>🕐 DST Handling Test Suite</h1>
    <p>Comprehensive testing of Daylight Saving Time handling in Pacific Time timezone utilities.</p>

    <!-- Current DST Status -->
    <div class="test-container">
        <div class="test-title">Current DST Status</div>
        <div id="current-dst-status"></div>
        <button class="test-button" onclick="checkCurrentDSTStatus()">Check Current DST Status</button>
    </div>

    <!-- DST Transition Tests -->
    <div class="test-container">
        <div class="test-title">DST Transition Tests</div>
        <button class="test-button" onclick="testSpringForward()">Test Spring Forward (PST → PDT)</button>
        <button class="test-button" onclick="testFallBack()">Test Fall Back (PDT → PST)</button>
        <button class="test-button" onclick="testAllDSTTransitions()">Test All 2024-2026 Transitions</button>
        <div class="test-results" id="dst-transition-results"></div>
    </div>

    <!-- Date Change Detection During DST -->
    <div class="test-container">
        <div class="test-title">Date Change Detection During DST</div>
        <button class="test-button" onclick="testDateChangeOnSpringForward()">Test Date Change on Spring Forward</button>
        <button class="test-button" onclick="testDateChangeOnFallBack()">Test Date Change on Fall Back</button>
        <button class="test-button" onclick="testMidnightDuringDST()">Test Midnight Detection During DST</button>
        <div class="test-results" id="dst-date-change-results"></div>
    </div>

    <!-- DST Boundary Analysis -->
    <div class="test-container">
        <div class="test-title">DST Boundary Analysis</div>
        <button class="test-button" onclick="analyzeDSTBoundaries()">Analyze DST Boundaries 2024-2026</button>
        <div class="test-results" id="dst-boundary-results"></div>
    </div>

    <!-- Load Required Scripts -->
    <script src="utils/timezone.js"></script>
    <script src="utils/date-change-manager.js"></script>

    <script>
        // DST transition dates for testing (US Pacific Time)
        const dstTransitions = {
            2024: {
                spring: { date: '2024-03-10', time: '02:00', description: 'Spring Forward: 2:00 AM → 3:00 AM' },
                fall: { date: '2024-11-03', time: '02:00', description: 'Fall Back: 2:00 AM → 1:00 AM' }
            },
            2025: {
                spring: { date: '2025-03-09', time: '02:00', description: 'Spring Forward: 2:00 AM → 3:00 AM' },
                fall: { date: '2025-11-02', time: '02:00', description: 'Fall Back: 2:00 AM → 1:00 AM' }
            },
            2026: {
                spring: { date: '2026-03-08', time: '02:00', description: 'Spring Forward: 2:00 AM → 3:00 AM' },
                fall: { date: '2026-11-01', time: '02:00', description: 'Fall Back: 2:00 AM → 1:00 AM' }
            }
        };

        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `status-${type}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            container.appendChild(resultDiv);
            container.scrollTop = container.scrollHeight;
        }

        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        // Check if a date is in DST
        function isDST(date) {
            const jan = new Date(date.getFullYear(), 0, 1);
            const jul = new Date(date.getFullYear(), 6, 1);
            const janOffset = jan.getTimezoneOffset();
            const julOffset = jul.getTimezoneOffset();
            return Math.max(janOffset, julOffset) !== date.getTimezoneOffset();
        }

        // Get timezone offset for Pacific Time at a specific date
        function getPacificOffset(date) {
            const formatter = new Intl.DateTimeFormat('en-US', {
                timeZone: 'America/Los_Angeles',
                timeZoneName: 'short'
            });
            const parts = formatter.formatToParts(date);
            const timeZoneName = parts.find(p => p.type === 'timeZoneName').value;
            return timeZoneName; // Returns 'PST' or 'PDT'
        }

        function checkCurrentDSTStatus() {
            const statusContainer = document.getElementById('current-dst-status');
            
            try {
                const now = new Date();
                const pacificTime = window.SnapTimezone.getPacificTime();
                const pacificOffset = getPacificOffset(now);
                const isDSTActive = pacificOffset === 'PDT';
                
                statusContainer.innerHTML = `
                    <div class="status-info">
                        <strong>Current Time Analysis:</strong><br>
                        UTC Time: ${now.toISOString()}<br>
                        Pacific Time: ${pacificTime.toString()}<br>
                        Timezone: ${pacificOffset} (${isDSTActive ? 'Daylight Saving Time' : 'Standard Time'})<br>
                        DST Active: ${isDSTActive ? '✅ Yes' : '❌ No'}
                    </div>
                `;
            } catch (error) {
                statusContainer.innerHTML = `<div class="status-error">Error: ${error.message}</div>`;
            }
        }

        function testSpringForward() {
            clearResults('dst-transition-results');
            addResult('dst-transition-results', 'Testing Spring Forward transition (PST → PDT)...', 'info');
            
            // Test the day before, day of, and day after spring forward
            const springDate = new Date('2025-03-09T10:00:00Z'); // March 9, 2025
            
            for (let i = -1; i <= 1; i++) {
                const testDate = new Date(springDate);
                testDate.setDate(springDate.getDate() + i);
                
                const pacificOffset = getPacificOffset(testDate);
                const formatter = new Intl.DateTimeFormat('en-US', {
                    timeZone: 'America/Los_Angeles',
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: false
                });
                
                const pacificTimeStr = formatter.format(testDate);
                addResult('dst-transition-results', 
                    `${testDate.toDateString()}: ${pacificTimeStr} ${pacificOffset}`, 'info');
            }
            
            addResult('dst-transition-results', 'Spring Forward test completed', 'success');
        }

        function testFallBack() {
            clearResults('dst-transition-results');
            addResult('dst-transition-results', 'Testing Fall Back transition (PDT → PST)...', 'info');
            
            // Test the day before, day of, and day after fall back
            const fallDate = new Date('2025-11-02T10:00:00Z'); // November 2, 2025
            
            for (let i = -1; i <= 1; i++) {
                const testDate = new Date(fallDate);
                testDate.setDate(fallDate.getDate() + i);
                
                const pacificOffset = getPacificOffset(testDate);
                const formatter = new Intl.DateTimeFormat('en-US', {
                    timeZone: 'America/Los_Angeles',
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: false
                });
                
                const pacificTimeStr = formatter.format(testDate);
                addResult('dst-transition-results', 
                    `${testDate.toDateString()}: ${pacificTimeStr} ${pacificOffset}`, 'info');
            }
            
            addResult('dst-transition-results', 'Fall Back test completed', 'success');
        }

        function testAllDSTTransitions() {
            clearResults('dst-transition-results');
            addResult('dst-transition-results', 'Testing all DST transitions 2024-2026...', 'info');
            
            let tableHTML = `
                <table class="dst-table">
                    <thead>
                        <tr>
                            <th>Year</th>
                            <th>Transition</th>
                            <th>Date</th>
                            <th>Before (PST/PDT)</th>
                            <th>After (PST/PDT)</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            Object.keys(dstTransitions).forEach(year => {
                const transitions = dstTransitions[year];
                
                // Test Spring Forward
                const springBefore = new Date(`${transitions.spring.date}T01:30:00-08:00`); // 1:30 AM PST
                const springAfter = new Date(`${transitions.spring.date}T03:30:00-07:00`);  // 3:30 AM PDT
                
                const springBeforeOffset = getPacificOffset(springBefore);
                const springAfterOffset = getPacificOffset(springAfter);
                const springStatus = (springBeforeOffset === 'PST' && springAfterOffset === 'PDT') ? '✅' : '❌';
                
                tableHTML += `
                    <tr class="dst-spring">
                        <td>${year}</td>
                        <td>Spring Forward</td>
                        <td>${transitions.spring.date}</td>
                        <td>${springBeforeOffset}</td>
                        <td>${springAfterOffset}</td>
                        <td>${springStatus}</td>
                    </tr>
                `;
                
                // Test Fall Back
                const fallBefore = new Date(`${transitions.fall.date}T01:30:00-07:00`); // 1:30 AM PDT
                const fallAfter = new Date(`${transitions.fall.date}T01:30:00-08:00`);  // 1:30 AM PST (after fall back)
                
                const fallBeforeOffset = getPacificOffset(fallBefore);
                const fallAfterOffset = getPacificOffset(fallAfter);
                const fallStatus = (fallBeforeOffset === 'PDT' && fallAfterOffset === 'PST') ? '✅' : '❌';
                
                tableHTML += `
                    <tr class="dst-fall">
                        <td>${year}</td>
                        <td>Fall Back</td>
                        <td>${transitions.fall.date}</td>
                        <td>${fallBeforeOffset}</td>
                        <td>${fallAfterOffset}</td>
                        <td>${fallStatus}</td>
                    </tr>
                `;
            });
            
            tableHTML += '</tbody></table>';
            
            const container = document.getElementById('dst-transition-results');
            container.innerHTML = tableHTML;
            
            addResult('dst-transition-results', 'All DST transitions tested', 'success');
        }

        function testDateChangeOnSpringForward() {
            clearResults('dst-date-change-results');
            addResult('dst-date-change-results', 'Testing date change detection on Spring Forward...', 'info');
            
            if (!window.DateChangeManager) {
                addResult('dst-date-change-results', 'DateChangeManager not available', 'warning');
                return;
            }
            
            // Simulate date change during spring forward
            const springDate = '2025-03-09'; // Spring forward date
            
            // Register a test callback
            let callbackTriggered = false;
            const testCallback = (prevDate, currDate) => {
                callbackTriggered = true;
                addResult('dst-date-change-results', 
                    `Date change callback triggered: ${prevDate} → ${currDate}`, 'success');
            };
            
            window.DateChangeManager.registerCallback(testCallback, 10);
            
            // Simulate the date change
            window.DateChangeManager.simulateDateChange(springDate);
            
            setTimeout(() => {
                if (callbackTriggered) {
                    addResult('dst-date-change-results', 
                        'Date change detection works correctly during Spring Forward', 'success');
                } else {
                    addResult('dst-date-change-results', 
                        'Date change detection failed during Spring Forward', 'warning');
                }
                
                window.DateChangeManager.unregisterCallback(testCallback);
            }, 100);
        }

        function testDateChangeOnFallBack() {
            clearResults('dst-date-change-results');
            addResult('dst-date-change-results', 'Testing date change detection on Fall Back...', 'info');
            
            if (!window.DateChangeManager) {
                addResult('dst-date-change-results', 'DateChangeManager not available', 'warning');
                return;
            }
            
            // Simulate date change during fall back
            const fallDate = '2025-11-02'; // Fall back date
            
            // Register a test callback
            let callbackTriggered = false;
            const testCallback = (prevDate, currDate) => {
                callbackTriggered = true;
                addResult('dst-date-change-results', 
                    `Date change callback triggered: ${prevDate} → ${currDate}`, 'success');
            };
            
            window.DateChangeManager.registerCallback(testCallback, 10);
            
            // Simulate the date change
            window.DateChangeManager.simulateDateChange(fallDate);
            
            setTimeout(() => {
                if (callbackTriggered) {
                    addResult('dst-date-change-results', 
                        'Date change detection works correctly during Fall Back', 'success');
                } else {
                    addResult('dst-date-change-results', 
                        'Date change detection failed during Fall Back', 'warning');
                }
                
                window.DateChangeManager.unregisterCallback(testCallback);
            }, 100);
        }

        function testMidnightDuringDST() {
            clearResults('dst-date-change-results');
            addResult('dst-date-change-results', 'Testing midnight detection during DST transitions...', 'info');
            
            if (!window.DateChangeManager) {
                addResult('dst-date-change-results', 'DateChangeManager not available', 'warning');
                return;
            }
            
            // Test midnight detection on DST transition dates
            const testDates = [
                '2025-03-09', // Spring forward
                '2025-11-02'  // Fall back
            ];
            
            testDates.forEach(dateStr => {
                // Create a date at 23:55 (near midnight)
                const testDate = new Date(`${dateStr}T23:55:00`);
                
                // Mock the DateChangeManager's midnight detection
                const isNearMidnight = window.DateChangeManager.isNearMidnightPacific();
                
                addResult('dst-date-change-results', 
                    `${dateStr} 23:55: Near midnight detection = ${isNearMidnight}`, 'info');
            });
            
            addResult('dst-date-change-results', 
                'Midnight detection during DST test completed', 'success');
        }

        function analyzeDSTBoundaries() {
            clearResults('dst-boundary-results');
            addResult('dst-boundary-results', 'Analyzing DST boundaries for potential issues...', 'info');
            
            const issues = [];
            const recommendations = [];
            
            // Check if our date string comparison method works across DST
            Object.keys(dstTransitions).forEach(year => {
                const spring = dstTransitions[year].spring.date;
                const fall = dstTransitions[year].fall.date;
                
                // Test date string generation on DST transition days
                const springDate = new Date(`${spring}T12:00:00`);
                const fallDate = new Date(`${fall}T12:00:00`);
                
                try {
                    // Simulate our date string generation
                    const springStr = springDate.toISOString().split('T')[0];
                    const fallStr = fallDate.toISOString().split('T')[0];
                    
                    if (springStr === spring && fallStr === fall) {
                        addResult('dst-boundary-results', 
                            `${year}: Date string generation works correctly`, 'success');
                    } else {
                        issues.push(`${year}: Date string mismatch on DST boundaries`);
                    }
                } catch (error) {
                    issues.push(`${year}: Error in date processing - ${error.message}`);
                }
            });
            
            // Recommendations
            recommendations.push('✅ Current implementation using Intl.DateTimeFormat is DST-safe');
            recommendations.push('✅ Date string comparison (YYYY-MM-DD) is timezone-independent');
            recommendations.push('✅ Pacific Time detection automatically handles PST/PDT transitions');
            recommendations.push('⚠️ Consider adding DST transition logging for debugging');
            recommendations.push('⚠️ Test real midnight transitions during DST changes');
            
            if (issues.length === 0) {
                addResult('dst-boundary-results', 'No DST boundary issues detected', 'success');
            } else {
                issues.forEach(issue => {
                    addResult('dst-boundary-results', issue, 'warning');
                });
            }
            
            addResult('dst-boundary-results', '<br><strong>Recommendations:</strong>', 'info');
            recommendations.forEach(rec => {
                addResult('dst-boundary-results', rec, 'info');
            });
        }

        // Initialize on page load
        window.addEventListener('load', () => {
            checkCurrentDSTStatus();
        });
    </script>
</body>
</html>
